import _ from 'lodash';
import memoize from 'memoizee';
import { z } from 'zod';

import {
  eachDayOfInterval,
  endOfDay,
  endOfMonth,
  endOfWeek,
  format,
  isSameMonth,
  startOfDay,
  startOfMonth,
  startOfWeek,
  subDays,
  subMonths,
  subYears,
} from 'date-fns';
import { ActionTypes, Option, Question, Topic } from '../../../../shared/types/manage';
import {
  getCalenderEventsValidator,
  getNextQuestionValidator,
  getUserDailyTrackerValidator,
  getUserTopicsValidator,
  topicListValidator,
  unsubscribeUserTopicValidator,
  upsertUserDailyTrackerValidator,
  upsertUserTopicValidator,
} from '../../../../shared/validators/manage.validator';
import { MID } from '../../@types/common';
import TopicModel from '../../models/Topics';
import UserAccountModel from '../../models/UserAccount';
import UserDailyTracker from '../../models/UserDailyTracker';
import UserTopicsModel from '../../models/UserTopics';
import mixpanel from '../../utils/mixpanel';
import { toObjectId } from '../../utils';
// import '../utils/manage-seeder';

const greenColorCode = '#25BA60';
const followUpScheduledQuestionExId = '111-67';

function isToday(createdAt: Date | undefined) {
  if (!createdAt) {
    return false;
  }
  const today = new Date();
  const date = new Date(createdAt);

  return date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth() && date.getDate() === today.getDate();
}

function getDaysDifference(endDate: Date = new Date(), startDate: Date = new Date()): number {
  // Calculate the difference in milliseconds
  const differenceInMilliseconds: number = endDate.getTime() - startDate.getTime();

  // Convert milliseconds to days
  const differenceInDays: number = differenceInMilliseconds / (1000 * 60 * 60 * 24);

  // Return the absolute value to ensure the difference is positive
  return Math.ceil(differenceInDays);
}

export async function getStreak(userId: MID, topicId: MID) {
  const result = await UserDailyTracker.aggregate([
    // Match documents for the given user where rawOptionData.color exists
    {
      $match: {
        user: toObjectId(userId),
        topic: toObjectId(topicId),
        'answers.rawOptionData.color': { $exists: true },
        isDeleted: false,
      },
    },
    // Convert date field to a standardized format
    {
      $project: {
        formattedDate: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
      },
    },
    // Sort by date in descending order
    { $sort: { formattedDate: -1 } },
    // Group and push dates into an array
    {
      $group: {
        _id: null,
        dates: { $push: '$formattedDate' },
      },
    },
    // Calculate the current streak
    {
      $project: {
        currentStreak: {
          $reduce: {
            input: '$dates',
            initialValue: { streak: 0, lastDate: null, broken: false },
            in: {
              $let: {
                vars: {
                  currentDate: '$$this',
                  lastDate: '$$value.lastDate',
                  currentStreak: '$$value.streak',
                  broken: '$$value.broken',
                },
                in: {
                  $cond: {
                    if: {
                      $or: [
                        { $eq: ['$$lastDate', null] }, // First date starts streak
                        {
                          $eq: [
                            {
                              $dateDiff: {
                                startDate: {
                                  $dateFromString: {
                                    dateString: '$$currentDate',
                                  },
                                },
                                endDate: {
                                  $dateFromString: { dateString: '$$lastDate' },
                                },
                                unit: 'day',
                              },
                            },
                            1,
                          ],
                        }, // If consecutive, increase streak
                      ],
                    },
                    then: {
                      streak: { $add: ['$$currentStreak', 1] },
                      lastDate: '$$currentDate',
                      broken: false,
                    },
                    else: {
                      streak: '$$currentStreak',
                      lastDate: '$$currentDate',
                      broken: true,
                    }, // Stop counting once a gap is found
                  },
                },
              },
            },
          },
        },
      },
    },
    // Extract the final streak value
    {
      $project: {
        _id: 0,
        currentStreak: {
          $cond: {
            if: '$currentStreak.broken',
            then: '$currentStreak.streak',
            else: '$currentStreak.streak',
          },
        },
      },
    },
  ]);
  return result.length ? result[0] : { longestStreak: 0, currentStreak: 0 };
}

const flattenObject = (obj: Record<string, any>, prefix = ''): Record<string, string> => {
  let result: Record<string, string> = {};

  _.forEach(obj, (value, key) => {
    const newKey = prefix ? `${prefix}.${key}` : key;

    if (_.isPlainObject(value)) {
      result = { ...result, ...flattenObject(value, newKey) };
    } else if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (_.isPlainObject(item)) {
          result = { ...result, ...flattenObject(item, `${newKey}[${index}]`) };
        } else {
          result[`${newKey}[${index}]`] = String(item);
        }
      });
    } else {
      result[newKey] = String(value);
    }
  });

  return result;
};

function processQuestion(questions: Question[], isCarePartner: boolean) {
  return _.map(questions, (tq) => {
    if (isCarePartner) {
      const { carePartnerText, options = [] } = tq;
      return {
        ...tq,
        question: carePartnerText || tq.question,
        options: options.map((option: Option) => ({
          ...option,
          value: option.carePartnerValue || option.value,
        })),
      };
    }
    return tq;
  });
}

export const getTopicList = async ({ topicIds = [], query, status = 'Published' }: z.infer<typeof topicListValidator>) => {
  let filters: any = topicIds.length ? { _id: { $in: topicIds } } : {};

  // Apply status filter
  if (status !== 'All') {
    filters.isDeleted = status === 'Draft'; // Draft = isDeleted:true, Published = isDeleted:false
  }

  if (query) {
    const searchString = new RegExp(query, 'gi');
    filters = _.assign({}, filters, {
      $or: [{ name: searchString }, { nickname: searchString }],
    });
  }

  // Get topics with category population (handles both single category and array of categories)
  const topics = await TopicModel.find(filters).populate('category').lean();

  // Get all unique tag IDs from the topics
  const tagIds = _.uniq(_.flatten(topics.map((topic) => topic.tags || [])));

  if (tagIds.length > 0) {
    // Fetch all tags in one query
    const { BlogTagModel } = require('../../models/Blog');
    const tags = await BlogTagModel.find({ _id: { $in: tagIds } }).lean();

    // Create a map of tag IDs to tag objects
    const tagMap = _.keyBy(tags, (tag) => tag._id.toString());

    // Replace tag IDs with tag objects in each topic
    return topics.map((topic) => ({
      ...topic,
      tags: (topic.tags || []).map((tagId) => {
        const tagIdStr = tagId.toString();
        return tagMap[tagIdStr] || tagIdStr;
      }),
    }));
  }

  return topics;
};

export const getUserTopics = (payload: z.infer<typeof getUserTopicsValidator>, userId: MID) => {
  const defaultFilters = { isDeleted: { $ne: true } };
  const filters = payload.topicId ? { topic: payload.topicId, ...defaultFilters } : defaultFilters;
  return UserTopicsModel.find({ user: userId, ...filters })
    .populate<{ topic: Topic; category: { logo: string; name: string }[] }>([{ path: 'topic', populate: { path: 'category' } }])
    .lean()
    .exec();
};

export const upsertUserTopic = async (payload: z.infer<typeof upsertUserTopicValidator>, userId: MID) => {
  const { topic, ...rest } = payload;
  console.log('rest', rest);
  memoizedGetUserTopics.delete({}, userId.toString());
  await UserAccountModel.findOneAndUpdate(
    { _id: userId, 'lastMangeImpressionMade.topic': { $ne: payload.topic } }, // Ensure topic is not already in array
    {
      $push: {
        lastMangeImpressionMade: {
          topic: payload.topic,
          date: new Date(),
          enableNotifications: true,
          isComplete: false,
        },
      },
    },
  );

  mixpanel.trackEvent('ManageSubscriptionQuestions', { ...flattenObject(payload), isDeleted: 'false' }, userId.toString());
  mixpanel.trackEvent('ManageSubscriptionTopics', { ...flattenObject(payload), isDeleted: 'false' }, userId.toString());
  return UserTopicsModel.updateOne({ user: userId, topic }, { $set: { ...rest, isDeleted: false } }, { upsert: true });
};

export const unsubscribeUserTopic = async (payload: z.infer<typeof unsubscribeUserTopicValidator>, userId: MID) => {
  // unsubscribe topic notifications from user;
  await UserAccountModel.findByIdAndUpdate(userId, {
    $pull: { lastMangeImpressionMade: { topic: payload.topic } },
  });
  mixpanel.trackEvent('ManageUnSubscriptionTopics', { ...flattenObject(payload), isDeleted: 'true' }, userId.toString());
  return UserTopicsModel.updateOne({ user: userId, topic: payload.topic }, { $set: { isDeleted: true } });
};

export const getUserDailyTrackers = (payload: z.infer<typeof getUserDailyTrackerValidator>, userId: MID) => {
  // const filters = payload.questionIds
  //   ? { 'answers.question': { $in: payload.questionIds } }
  //   : { date: { $gte: payload.startDate, $lte: payload.endDate } };
  const filters = {
    ...(payload.topicId ? { topic: payload.topicId } : {}),
    ...(payload.questionIds ? { 'answers.question': { $in: payload.questionIds } } : { date: { $gte: payload.startDate, $lte: payload.endDate } }),
  };
  return UserDailyTracker.find({ user: userId, ...filters })
    .populate({ path: 'answers.question' })
    .lean();
};

export const upsertUserDailyTracker = async (payload: z.infer<typeof upsertUserDailyTrackerValidator>, userId: MID) => {
  const { topic, date, ...rest } = payload;
  mixpanel.trackEvent('ManageUserDailyTracker', { ...flattenObject(payload) }, userId.toString());
  return UserDailyTracker.updateOne(
    {
      user: userId,
      topic,
      date: { $gte: startOfDay(date), $lte: endOfDay(date) },
    },
    { $set: { ...rest, date } },
    { upsert: true },
  );
};

export const getBehaviourAnswerDays = async (payload: any, userId: MID) => {
  const { optionId, trackingWindow = 5 } = payload;
  const startDate = startOfDay(subDays(new Date(), trackingWindow));
  const endDate = endOfDay(subDays(new Date(), 1));
  return UserDailyTracker.countDocuments({
    user: userId,
    'answers.selectedOption': optionId,
    date: { $gte: startDate, $lte: endDate },
  });
};

const memoizedGetUserTopics = memoize(getUserTopics, {
  promise: true,
  normalizer: (args) => {
    const [payload, userId] = args;
    return userId.toString();
  },
});

const getQuestionFromAction = async (
  currentQuestionId: string,
  selectedOptionExIds: string | string[],
  dailyTrackerQuestions: Question[],
  inactiveScheduleQuestionId: string[],
  topic: any,
  selectedWeekDay: Date,
  userId: any,
  scheduledQuestions: Question[],
  confetti: boolean,
) => {
  const currentQuestion = dailyTrackerQuestions.find((q) => {
    return q._id?.toString() === currentQuestionId;
  });
  if (!currentQuestion) throw new Error('Question not found');

  if (!currentQuestion.actions) {
    throw new Error('No actions found for the current question');
  }
  // const confetti =
  //   _.get(_.find(currentQuestion.options, { externalId: selectedOptionExIds[selectedOptionExIds.length - 1] }), 'color') === greenColorCode;
  const selectedAction =
    currentQuestion.actions.find(
      (action) => selectedOptionExIds.includes(action.metadata.optionExternalId) || action.metadata.optionExternalId === '*',
    ) || currentQuestion.actions[0];
  if (selectedAction?.actionType === ActionTypes.NextBehaviourQuestion) {
    const { choosenFrequency, trackingWindow, optionExternalId, nextQuestionExternalId } = selectedAction.metadata;

    const currentOption = currentQuestion.options?.find((option) => option.externalId === optionExternalId);
    const answeredDays = await getBehaviourAnswerDays({ optionId: currentOption?._id, trackingWindow }, userId);

    let nextQuestion = null;

    if (answeredDays + 1 === choosenFrequency && currentOption?.externalId === optionExternalId) {
      nextQuestion = dailyTrackerQuestions.find((q) => q.externalId === nextQuestionExternalId);
    } else {
      const nextBehaviourQuestion = dailyTrackerQuestions.find((q) => q.externalId === nextQuestionExternalId);
      if (!nextBehaviourQuestion) {
        throw new Error('Next behaviour question not found');
      }
      if (nextBehaviourQuestion.actions && nextBehaviourQuestion.actions.length > 0) {
        const nextQuestionExternalId = nextBehaviourQuestion.actions[0].metadata.nextQuestionExternalId;
        nextQuestion = dailyTrackerQuestions.find((q) => q.externalId === nextQuestionExternalId);
      } else {
        throw new Error('Next behaviour question has no actions');
      }
    }

    return {
      nextQuestion,
      shouldMarkCompleted: !nextQuestion,
      showConfetti: confetti,
    };
  } else if (selectedAction?.actionType === ActionTypes.NextQuestion) {
    const nextQuestionExternalId = selectedAction.metadata.nextQuestionExternalId;
    let nextQuestion = dailyTrackerQuestions.find((q) => q.externalId === nextQuestionExternalId);
    const dailyQuestion = topic?.dailyTrackerQuestions.find((question: any) => question.externalId === '111-55');
    let nextScheduleQuestion = scheduledQuestions.find((q) => q.externalId === nextQuestionExternalId);
    nextQuestion = _.isEmpty(nextQuestion) ? nextScheduleQuestion : nextQuestion;

    if (nextQuestion?.isSchedule && nextQuestion._id && inactiveScheduleQuestionId.includes(nextQuestion._id.toString())) {
      if (nextScheduleQuestion?.getNextCompletedQuestion && inactiveScheduleQuestionId.includes(nextQuestion._id.toString())) {
        const getNextCompletedQuestion = eval('(' + nextScheduleQuestion.getNextCompletedQuestion + ')');
        let userDailyQAnswers = await getUserDailyTrackers(
          {
            startDate: startOfDay(selectedWeekDay),
            endDate: endOfDay(selectedWeekDay),
            topicId: topic?._id.toString(),
          },
          userId,
        );
        // const color = userDailyQAnswers[0]?.answers.find((answer) => answer.question.toString() === dailyQuestion?._id.toString())?.rawOptionData
        //   ?.color;
        const filteredData = _.find(userDailyQAnswers[0]?.answers, (item) => _.has(item.rawOptionData, 'color'));
        const nextCompletedQuestionExId = getNextCompletedQuestion(filteredData?.rawOptionData?.color);
        nextQuestion = dailyTrackerQuestions.find((q) => q.externalId === nextCompletedQuestionExId);
        // const confetti =
        //   _.get(_.find(nextQuestion?.options, { externalId: selectedOptionExIds[selectedOptionExIds.length - 1] }), 'color') === greenColorCode;
        return {
          nextQuestion,
          shouldMarkCompleted: !nextQuestion,
          showConfetti: confetti,
        };
      }
      return getQuestionFromAction(
        nextQuestion._id?.toString(),
        [],
        dailyTrackerQuestions,
        inactiveScheduleQuestionId,
        topic,
        selectedWeekDay,
        userId,
        scheduledQuestions,
        confetti,
      );
    }
    if (currentQuestion?.getNextCompletedQuestion) {
      const getNextCompletedQuestion = eval('(' + currentQuestion.getNextCompletedQuestion + ')');
      let userDailyQAnswers = await getUserDailyTrackers(
        {
          startDate: startOfDay(selectedWeekDay),
          endDate: endOfDay(selectedWeekDay),
          topicId: topic?._id.toString(),
        },
        userId,
      );
      // const color = userDailyQAnswers[0]?.answers.find((answer) => answer.question.toString() === dailyQuestion?._id.toString())?.rawOptionData
      //   ?.color;
      const filteredData = _.find(userDailyQAnswers[0]?.answers, (item) => _.has(item.rawOptionData, 'color'));
      const nextCompletedQuestionExId = getNextCompletedQuestion(filteredData?.rawOptionData?.color);
      nextQuestion = dailyTrackerQuestions.find((q) => q.externalId === nextCompletedQuestionExId);
      // const confetti =
      //   _.get(_.find(nextQuestion?.options, { externalId: selectedOptionExIds[selectedOptionExIds.length - 1] }), 'color') === greenColorCode;
      return {
        nextQuestion,
        shouldMarkCompleted: !nextQuestion,
        showConfetti: confetti,
      };
    }

    return {
      nextQuestion,
      shouldMarkCompleted: !nextQuestion,
      showConfetti: confetti,
    };
  }

  if (selectedAction?.actionType === ActionTypes.Call) {
    return {
      actionType: ActionTypes.Call,
      phone: selectedAction.metadata.phone,
    };
  }

  return {
    nextQuestion: null,
    shouldMarkCompleted: true,
    showConfetti: confetti,
  };
};
export const clearCache = () => {
  memoizedGetUserTopics.clear();
};

const getTotalQuestion = (dailyTrackerQuestions: Question[], scheduledQuestions: Question[], currentQuestion: string) => {
  let count = 1;
  let currentQuestionId = currentQuestion;

  while (currentQuestionId) {
    const currentQuestionData = _.find(dailyTrackerQuestions, (q) => (q.externalId as string) === currentQuestionId);
    if (!currentQuestionData) break; // Stop if no question is found
    count++; // Increment the question count

    const action = currentQuestionData?.actions?.[0];

    if (!action) break; // Stop if no valid next action is found

    currentQuestionId = action.metadata.nextQuestionExternalId || currentQuestionData?.actions?.[1]?.metadata?.nextQuestionExternalId; // Move to the next question
  }

  return count + scheduledQuestions.length;
};

export const getNextQuestion = async (payload: z.infer<typeof getNextQuestionValidator>, userId: MID) => {
  try {
    const { currentQuestionId, selectedOptionExIds = [], selectedWeekDay, topicId, getQuestionCount = false } = payload || {};
    const userTopics = await memoizedGetUserTopics({}, userId);
    if (!userTopics.length) {
      throw new Error('No topics found for user.');
    }
    const userTopic = _.find(userTopics, (userTopic) => userTopic?.topic?._id?.toString() === topicId) || userTopics[0];
    const { topic } = userTopic;
    const isCarePartner = _.get(userTopic, 'isCarePartner', false);

    if (!topic || !topic._id) {
      throw new Error('Topic not found or missing _id.');
    }

    // const topics = await getTopicList({ topicIds: [topic._id.toString()] });
    // schedule question
    const scheduledQuestions = topic.dailyTrackerQuestions.filter((q) => q.isSchedule);

    let userScheduleQAnswers = await getUserDailyTrackers(
      {
        questionIds: _.compact(_.map(scheduledQuestions, (q) => q._id?.toString())),
        topicId: topic?._id.toString(),
      },
      userId,
    );

    // userScheduleQAnswers = userScheduleQAnswers.filter((q) => !isToday(q.date));

    const ACTIVITY_QUESTION_ID = '681a2f558c9ece01dff16bb4';

    userScheduleQAnswers = userScheduleQAnswers.filter((q) => {
      const notToday = !isToday(new Date(q.date));
      const hasTargetQuestion = q.answers?.some((ans) => ans.question?.toString() === ACTIVITY_QUESTION_ID);
      return notToday || hasTargetQuestion;
    });

    let inactiveScheduleQuestionId = scheduledQuestions
      .filter((sq) => {
        const differenceInDays: number = getDaysDifference(selectedWeekDay, userTopic?.createdAt);
        const _userScheduleQAnswers = _.filter(userScheduleQAnswers, (item) => _.some(item.answers, { question: sq._id }));
        if (sq.recurringQuestion && _userScheduleQAnswers?.length) {
          const interval = sq?.recurringDay || sq?.delay;
          console.log({
            differenceInDays: Math.abs(differenceInDays),
            delay: sq.delay,
          });
          if (interval && (differenceInDays <= 0 || differenceInDays % interval !== 0)) {
            console.log({ recurre: differenceInDays % interval });
            return true;
          }
          return false;
        }
        if (sq.delay === 0 && sq.duration === 0 && differenceInDays < 0 && !_userScheduleQAnswers.length) {
          return true;
        }
        // @ts-ignore
        const delay = sq.delay ?? 0;
        const duration = sq.duration ?? 0;
        return differenceInDays < delay || differenceInDays > delay + duration || _userScheduleQAnswers.length;
      })
      .map((q) => q._id?.toString());

    // const currentQuestionIndex = topic.dailyTrackerQuestions.findIndex(d => d._id?.toString() === currentQuestionId);

    const _activeScheduleQuestions = scheduledQuestions.filter((q, i) => !inactiveScheduleQuestionId.includes(q._id?.toString()));
    // const activeScheduleQuestions = processQuestion(_activeScheduleQuestions, isCarePartner);

    // if (activeScheduleQuestions.length) {
    //    return {
    //     nextQuestion: activeScheduleQuestions[0],
    //     shouldMarkCompleted: false,
    //     showConfetti: false,
    //   };
    // }

    // const dailyTrackerQuestions = topic.dailyTrackerQuestions.filter((dt) => !inactiveScheduleQuestionId.includes(dt._id?.toString()));
    const _dailyTrackerQuestions = topic.dailyTrackerQuestions.filter((dt) => !inactiveScheduleQuestionId.includes(dt._id?.toString()));
    const dailyTrackerQuestions = processQuestion(_dailyTrackerQuestions, isCarePartner);
    if (getQuestionCount) {
      const currentActions = _.find(dailyTrackerQuestions, (dq) => dq._id?.toString() === currentQuestionId)?.actions;
      const nextQuestionId = _.find(currentActions, (action) => action.metadata.optionExternalId === _.head(selectedOptionExIds))?.metadata
        ?.nextQuestionExternalId;
      const totalQuestion = getTotalQuestion(_dailyTrackerQuestions, _activeScheduleQuestions, nextQuestionId);
      console.log({
        totalQuestion,
        nextQuestionId,
        currentQuestionId,
        currentActions,
      });
      return totalQuestion;
    }
    if (!currentQuestionId) {
      return {
        nextQuestion: dailyTrackerQuestions[0].externalId === followUpScheduledQuestionExId ? dailyTrackerQuestions[1] : dailyTrackerQuestions[0],
        showConfetti: false,
      };
    } else {
      const _scheduledQuestions = processQuestion(scheduledQuestions, isCarePartner);
      inactiveScheduleQuestionId = _.includes(inactiveScheduleQuestionId, '672c99e76c300a9c6eae79cf')
        ? [...inactiveScheduleQuestionId, '673ca3b31c1bd160fda8eec1']
        : inactiveScheduleQuestionId;
      const currentQuestion = dailyTrackerQuestions.find((q) => {
        return q._id?.toString() === currentQuestionId;
      });
      const confetti =
        _.get(
          _.find(currentQuestion?.options, {
            externalId: selectedOptionExIds[selectedOptionExIds.length - 1],
          }),
          'color',
        ) === greenColorCode;
      return getQuestionFromAction(
        currentQuestionId,
        selectedOptionExIds,
        [...(dailyTrackerQuestions as Question[]), ...(_scheduledQuestions as Question[])],
        inactiveScheduleQuestionId as string[],
        topic,
        selectedWeekDay as Date,
        userId,
        _scheduledQuestions as Question[],
        confetti as boolean,
      );
    }
  } catch (err) {
    console.error(err);
    return {};
  }
};

export const getCalenderEvents = async (payload: z.infer<typeof getCalenderEventsValidator>, userId: MID) => {
  const { startDate, endDate, topicId } = payload;

  const userDailyTrackers = await getUserDailyTrackers(
    {
      startDate,
      endDate,
      ...(topicId ? { topicId } : {}),
    },
    userId,
  );

  const eventsByDate: Record<string, string[]> = {};
  userDailyTrackers.forEach((event) => {
    const key = format(event.date, 'yyyy-MM-dd');
    if (!eventsByDate[key]) {
      eventsByDate[key] = [];
    }
    eventsByDate[key].push(...event.answers.map((a) => a.rawOptionData?.color as string).filter(Boolean));
  });
  return {
    events: eventsByDate,
    startDate,
    endDate,
  };
};

export const getCalenderWeeksAndColor = async (payload: z.infer<typeof getCalenderEventsValidator>, useId: MID) => {
  const { startDate, endDate, startDateOfMonth, topicId } = payload;

  const week = eachDayOfInterval({
    start: startOfWeek(startOfMonth(new Date(startDateOfMonth as string)), {}),
    end: endOfWeek(endOfMonth(new Date(startDateOfMonth as string)), {}),
  });
  const { events } = await getCalenderEvents({ startDate, endDate, ...(topicId ? { topicId } : {}) }, useId);
  const eventsByMonth: Record<string, string[]> = {};

  Object.entries(events ?? {}).forEach(([key, value]) => {
    if (isSameMonth(new Date(key), new Date(startDateOfMonth as string))) {
      eventsByMonth[key] = Array.isArray(value) ? value : [];
    }
  });

  const calculateColorsInPercent = () => {
    const colorCounts: Record<string, number> = {};
    let totalUniqueColors = 0;

    Object.values(eventsByMonth ?? {}).forEach((colors) => {
      colors.forEach((color) => {
        if (!colorCounts[color]) {
          colorCounts[color] = 0;
        }
        colorCounts[color] += 1;
        totalUniqueColors += 1;
      });
    });

    return Object.entries(colorCounts).map(([color, count]) => ({
      color,
      percent: (count / totalUniqueColors) * 100,
    }));
  };
  const colorsInPercent = calculateColorsInPercent();

  return { colorsInPercent, week, eventsByMonth };
};

export const getProgressReportHtml = async (payload: any, userId: MID) => {
  // Fetch the topic to get question and option IDs
  const topic = await TopicModel.findOne({ name: 'Asthma' });
  if (!topic) {
    throw new Error('Topic not found');
  }

  const now = new Date();
  const startDate = startOfMonth(subMonths(now, 3)); // 3 months before current month = 4 full months incl. current
  const endDate = endOfDay(now);

  // Get question IDs from the fetched topic
  const symptomQuestion = topic.dailyTrackerQuestions.find((q) => q.externalId === '111-56');
  const asthmaControlQuestion = topic.dailyTrackerQuestions.find((q) => q.externalId === '111-61');
  const unplannedVisitQuestion = topic.dailyTrackerQuestions.find((q) => q.externalId === '111-59');
  const missedWorkQuestion = topic.dailyTrackerQuestions.find((q) => q.externalId === '111-60');
  const checkAllQuestion = topic.dailyTrackerQuestions.find((q) => q.externalId === '111-62');

  const symptomQuestionIds = symptomQuestion?._id ? [symptomQuestion._id.toString()] : [];
  const asthmaControlQuestionIds = asthmaControlQuestion?._id ? [asthmaControlQuestion._id.toString()] : [];

  // Get option IDs from the fetched topic
  const oralSteroidUseOptionId = checkAllQuestion?.options?.find((opt) => opt.externalId === '111-62-3')?._id?.toString() || '';
  const unplannedVisitOptionId = unplannedVisitQuestion?.options?.find((opt) => opt.externalId === '111-59-1')?._id?.toString() || '';
  const hospitalAdmission = checkAllQuestion?.options?.find((opt) => opt.externalId === '111-62-2')?._id?.toString() || '';
  const overNigthStayOptionId = checkAllQuestion?.options?.find((opt) => opt.externalId === '111-62-1')?._id?.toString() || '';
  const missedSchoolOrWorkOptionId = missedWorkQuestion?.options?.find((opt) => opt.externalId === '111-60-1')?._id?.toString() || '';
  const endTrackerDate = endOfDay(now);
  const startTrackerDate = startOfDay(subYears(new Date(), 1)); // Exactly 1 year ago, at 00:00:00

  const optionIds = [oralSteroidUseOptionId, unplannedVisitOptionId, hospitalAdmission, overNigthStayOptionId, missedSchoolOrWorkOptionId];

  const pipeline = [
    {
      $match: {
        user: toObjectId(userId),
        topic: toObjectId(topic._id),
        date: { $gte: startTrackerDate, $lte: endTrackerDate },
        'answers.selectedOption': {
          $in: _.map(optionIds, (id) => toObjectId(id)),
        },
      },
    },
    { $unwind: '$answers' },
    { $unwind: '$answers.selectedOption' },
    {
      $match: {
        'answers.selectedOption': {
          $in: _.map(optionIds, (id) => toObjectId(id)),
        },
      },
    },
    {
      $group: {
        _id: '$answers.selectedOption',
        count: { $sum: 1 },
      },
    },
  ];

  const counts = await UserDailyTracker.aggregate(pipeline);

  const optionCounts = counts?.reduce((acc, curr) => {
    acc[curr._id] = curr.count;
    return acc;
  }, {});

  const symptomTrackingAnswer = await getUserDailyTrackers(
    { startDate, endDate, questionIds: symptomQuestionIds, topicId: topic._id.toString() },
    userId,
  );
  const labels: string[] = [];
  for (let i = 3; i >= 0; i--) {
    labels.push(format(subMonths(now, i), 'MMMM'));
  }

  const symptomCounts: Record<string, Record<string, number>> = {};

  symptomTrackingAnswer.forEach((entry: any) => {
    const month = format(new Date(entry.date), 'MMMM');

    entry.answers
      .filter((answer: any) => symptomQuestionIds.includes(answer.question?.toString())) // ✅ only desired questions
      .forEach((answer: any) => {
        const symptom = answer.rawOptionData?.value;
        if (!symptom) return;

        if (!symptomCounts[symptom]) symptomCounts[symptom] = {};
        if (!symptomCounts[symptom][month]) symptomCounts[symptom][month] = 0;

        symptomCounts[symptom][month]++;
      });
  });

  const backgroundColors = ['#3498db', '#e67e22', '#27ae60', '#9b59b6', '#e84393', '#2ecc71', '#f1c40f', '#34495e'];

  const symptomTrackingDatasets = Object.entries(symptomCounts).map(([symptom, monthData], idx) => ({
    label: symptom,
    data: labels.map((month) => monthData[month] || 0),
    backgroundColor: backgroundColors[idx % backgroundColors.length],
  }));

  const asthmaControlAnswer = await getUserDailyTrackers(
    { startDate, endDate, questionIds: asthmaControlQuestionIds, topicId: topic._id.toString() },
    userId,
  );
  const controlCounts: Record<string, Record<string, number>> = {};
  asthmaControlAnswer.forEach((entry: any) => {
    const month = format(new Date(entry.date), 'MMMM');

    entry.answers
      .filter((answer: any) => asthmaControlQuestionIds.includes(answer.question?.toString())) // ✅ only desired questions
      .forEach((answer: any) => {
        const control = answer.rawOptionData?.value;
        if (!control) return;

        if (!controlCounts[control]) controlCounts[control] = {};
        if (!controlCounts[control][month]) controlCounts[control][month] = 0;

        controlCounts[control][month]++;
      });
  });

  const controlDatasets = Object.entries(controlCounts).map(([control, monthData], idx) => ({
    label: control,
    data: labels.map((month) => monthData[month] || 0),
    backgroundColor: backgroundColors[idx % backgroundColors.length],
  }));

  console.log({ datasets: JSON.stringify(controlDatasets, null, 2), labels });

  // Chart.js configurations for bar charts
  const symptomTrackingChartConfig = {
    type: 'bar',
    data: {
      labels,
      datasets: symptomTrackingDatasets,
    },
    options: {
      responsive: true,
      scales: {
        y: { beginAtZero: true },
      },
      plugins: {
        legend: {
          display: true,
          position: 'bottom',
        },
      },
    },
  };

  const asthmaControlChartConfig = {
    type: 'bar',
    data: {
      labels,
      datasets: controlDatasets,
    },
    options: {
      responsive: true,
      scales: {
        y: { beginAtZero: true },
      },
      plugins: {
        legend: {
          display: true,
          position: 'bottom',
        },
      },
    },
  };

  // Convert pie charts to bar charts as requested
  const asthmaControlBarConfig = {
    type: 'bar',
    data: {
      labels: ['None', 'Poorly', 'Somewhat', 'Well', 'Completely'],
      datasets: [
        {
          label: 'Asthma Control',
          data: [25, 0, 50, 0, 25],
          backgroundColor: ['#e74c3c', '#e67e22', '#2ecc71', '#f1c40f', '#3498db'],
        },
      ],
    },
    options: {
      responsive: true,
      scales: {
        y: { beginAtZero: true },
      },
      plugins: {
        legend: {
          display: true,
          position: 'bottom',
        },
      },
    },
  };

  const impactBarConfig = {
    type: 'bar',
    data: {
      labels: ['Constant impact', 'Often impact', 'Occasional Impact', 'No problem'],
      datasets: [
        {
          label: 'Impact on Goals',
          data: [25, 50, 25, 0],
          backgroundColor: ['#e67e22', '#2ecc71', '#3498db', '#e74c3c'],
        },
      ],
    },
    options: {
      responsive: true,
      scales: {
        y: { beginAtZero: true },
      },
      plugins: {
        legend: {
          display: true,
          position: 'bottom',
        },
      },
    },
  };

  const htmlContent = `
  <!DOCTYPE html>
  <html>
    <head>
      <meta charset="utf-8">
      <title>MyProgress Report</title>
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      <style>
        body {
          font-family: Arial, sans-serif;
          padding: 16px;
          color: #000;
        }
        h2 {
          margin-bottom: 8px;
          color: #004987;
        }
        .container {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
        }
        .left-col, .right-col {
          width: 48%;
        }
        .chart-container {
          margin-bottom: 32px;
          border: 1px solid #ccc;
          padding: 12px;
        }
        .chart-canvas {
          width: 100% !important;
          height: 300px !important;
        }
        .discussion {
          font-size: 13px;
          margin-bottom: 16px;
        }
        .discussion strong {
          background-color: yellow;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 8px;
        }
        td, th {
          border: 1px solid #000;
          padding: 6px 10px;
          font-size: 13px;
          text-align: left;
        }
      </style>
    </head>
    <body>
       <div class="container">
        <div class="left-col">
          <div class="chart-container">
            <h3>Symptom Tracking</h3>
            <canvas id="symptomChart" class="chart-canvas"></canvas>
          </div>

          <div class="chart-container">
            <h3>Asthma Control</h3>
            <canvas id="asthmaControlChart" class="chart-canvas"></canvas>
          </div>
        </div>

        <div class="right-col">
          <div class="chart-container">
            <h3>myAsthma Control - Last 4 months</h3>
            <canvas id="asthmaControlBarChart" class="chart-canvas"></canvas>
          </div>

          <div class="chart-container">
            <h3>Impact of Asthma on myGoal</h3>
            <canvas id="impactBarChart" class="chart-canvas"></canvas>
          </div>

          <div class="discussion">
            <p><strong>Items to discuss at my next appointment:</strong></p>
            <ul>
              <li>Action plan: create/review (create if "no")</li>
              <li>Severity (if "no" or "I don't know")</li>
              <li>Are there any additional meds I should take?</li>
              <li>Do I need any other tests? Breathing? Blood?</li>
              <li>Any specialists?</li>
            </ul>
            <p><strong>Year to date</strong> asthma related incidents (Running 365):</p>
            <table>
              <tr><th>Incident</th><th>Occurred?</th></tr>
              <tr><td>Oral steroid use</td><td>${optionCounts[oralSteroidUseOptionId] || 0}</td></tr>
              <tr><td>ER or unplanned visit</td><td>${optionCounts[unplannedVisitOptionId] || 0}</td></tr>
              <tr><td>Overnight stay</td><td>${optionCounts[overNigthStayOptionId] || 0}</td></tr>
              <tr><td>Hospital Admission</td><td>${optionCounts[hospitalAdmission] || 0}</td></tr>
              <tr><td>Missed School or work</td><td>${optionCounts[missedSchoolOrWorkOptionId] || 0}</td></tr>
            </table>
          </div>
        </div>
      </div>

      <script>
        // Symptom Tracking Chart
        const symptomCtx = document.getElementById('symptomChart').getContext('2d');
        new Chart(symptomCtx, ${JSON.stringify(symptomTrackingChartConfig)});

        // Asthma Control Chart
        const asthmaCtx = document.getElementById('asthmaControlChart').getContext('2d');
        new Chart(asthmaCtx, ${JSON.stringify(asthmaControlChartConfig)});

        // Asthma Control Bar Chart
        const asthmaBarCtx = document.getElementById('asthmaControlBarChart').getContext('2d');
        new Chart(asthmaBarCtx, ${JSON.stringify(asthmaControlBarConfig)});

        // Impact Bar Chart
        const impactBarCtx = document.getElementById('impactBarChart').getContext('2d');
        new Chart(impactBarCtx, ${JSON.stringify(impactBarConfig)});
      </script>
    </body>
  </html>
  `;
  return htmlContent;
};
