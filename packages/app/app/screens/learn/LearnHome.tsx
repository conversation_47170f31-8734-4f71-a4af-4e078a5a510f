import LoadingScreen from "@components/common/LoadingScreen";
import Screen from "@components/common/Screen";
import BlogLayoutList from "../../components/learn/BlogLayoutList";
import FavoritedBlogsList from "../../components/learn/FavoritedBlogsList";
import Tabs from "@components/common/Tabs";
import AppText from "@components/common/AppText";
import ManageHomeScreenPills from "@screens/manage/ManageHomeScreenPills";
import { ButterFlyIcon } from "@screens/manage/ManageHomeScreen";
import { useSession } from "@hooks/persistUser";
import { trpc } from "@providers/RootProvider";
import mixpanel from "@utils/mixpanel";
import clsx from "clsx";
import { useEffect, useState } from "react";
import { Platform, ScrollView, View } from "react-native";
import { z } from "zod";
import { BlogStatus } from "../../../../shared/types/Blog";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Schema for blog query parameters
export const blogQuerySchema = z.object({
  status: z.nativeEnum(BlogStatus),
  tags: z.array(z.string()), // Array of tag ObjectIds
});

export default function LearnHomeScreen() {
  const [selectedTab, setSelectedTab] = useState(0); // 0 = Blogs, 1 = Favorites
  const [selectedTopic, setSelectedTopic] = useState("");
  const { user } = useSession();

  const blogDashboard = trpc.blog.getBlogDashboard.useQuery();

  // Fetch blog layouts for the Blogs tab
  const blogLayouts = trpc.blog.getBlogLayouts.useQuery({
    status: BlogStatus.PUBLISHED,
  });

  // Fetch data for the header pills
  const topics = trpc.manage.getTopics.useQuery({ status: "Published" });
  const userTopics = trpc.manage.getUserTopics.useQuery({});
  const streak = trpc.manage.getStreak.useQuery(
    {
      topicId:
        selectedTopic || userTopics?.data?.[0]?.topic?._id?.toString() || "",
    },
    { enabled: !!selectedTopic || !!userTopics?.data?.[0]?.topic?._id }
  );

  useEffect(() => {
    (async () => {
      await mixpanel.trackEvent(
        "Learn screen view",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
        },
        String(user?._id),
        "v2"
      );
    })();
  }, []);

  if (blogDashboard.isLoading || blogLayouts.isLoading || topics.isLoading) {
    return (
      <View className="flex-1">
        <LoadingScreen />
      </View>
    );
  }

  return (
    <Screen
      headerClasses="mx-4"
      className={clsx("flex flex-1", Platform.OS === "android" && "pt-10")}
    >
      <ScrollView>
        <View className="flex">
          {/* Header Section with Learn Title and Pills */}
          <View className="mx-4 mb-3">
            <View className="flex-row items-center justify-between ">
              <AppText className="text-3xl font-montserratBold text-[#004987]">
                Learn
              </AppText>
              <ButterFlyIcon width={41} height={41} />
            </View>

            <View className="">
              <ManageHomeScreenPills
                topics={topics?.data ?? []}
                streak={streak.data}
                userTopics={userTopics.data ?? []}
                selectedTopic={
                  selectedTopic || userTopics?.data?.[0]?.topic?._id?.toString()
                }
                onTopicSelect={(topicId) => {
                  setSelectedTopic(topicId);
                  AsyncStorage.setItem("selectedTopic", topicId);
                }}
              />
            </View>
          </View>

          {/* Tabs for Blogs and Favorites */}
          <View className="mx-4">
            <Tabs
              tabState={[selectedTab, setSelectedTab]}
              tabs={["Blogs", "Favorites"]}
              selectedbgClass="bg-[#0B79D3]"
              shadowStyle={{
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 2,
              }}
            />
          </View>

          <View className="mx-4">
            {selectedTab === 0 ? (
              <BlogLayoutList data={blogLayouts.data} />
            ) : (
              <FavoritedBlogsList />
            )}
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
}
