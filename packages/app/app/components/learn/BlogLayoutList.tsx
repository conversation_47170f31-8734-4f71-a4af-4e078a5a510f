import { useState } from "react";
import { View, Image } from "react-native";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import BlogListItem from "./BlogListItem";
import { Plus, Minus } from "lucide-react-native";
import { RouterOutput } from "../../../../shared";
import { Blog } from "../../../../shared/types/Blog";

type Props = {
  data: RouterOutput["blog"]["getBlogLayouts"] | undefined;
};

export default function BlogLayoutList({ data }: Props) {
  // Start with all sections collapsed
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set()
  );

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  if (!data || data.length === 0) {
    return (
      <View className="mt-4">
        <AppText className="text-center text-gray-500">
          No blog sections available
        </AppText>
      </View>
    );
  }

  // Sort sections by sequence
  const sortedSections = [...data].sort(
    (a, b) => (a.sequence || 0) - (b.sequence || 0)
  );

  return (
    <View className="mt-4">
      {sortedSections.map((section) => {
        const isExpanded = expandedSections.has(section._id!.toString());
        const sectionId = section._id!.toString();

        return (
          <View key={sectionId} className="mb-4">
            {/* Section Header */}
            <Pressable
              actionTag="blog section toggle"
              className="bg-white rounded-xl p-4 mb-2 border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
              style={{
                elevation: 3,
                shadowColor: "#004987",
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.15,
                shadowRadius: 6,
              }}
              onPress={() => toggleSection(sectionId)}
            >
              <View className="flex flex-row items-center justify-between">
                <View className="flex flex-row items-center flex-1">
                  {section.image && (
                    <Image
                      source={{ uri: section.image }}
                      className="w-10 h-10 rounded-lg mr-4"
                      resizeMode="cover"
                    />
                  )}
                  <AppText
                    className="text-lg font-montserratSemiBold text-primary flex-1"
                    numberOfLines={2}
                  >
                    {section.sectionName}
                  </AppText>
                </View>
                <View className="ml-3">
                  {isExpanded ? (
                    <Minus size={24} color="#004987" />
                  ) : (
                    <Plus size={24} color="#004987" />
                  )}
                </View>
              </View>
            </Pressable>

            {/* Section Content */}
            {isExpanded && (
              <View className="ml-10">
                {section.blogs && section.blogs.length > 0 ? (
                  section.blogs
                    .filter((blog): blog is Blog => blog && blog._id) // Type guard to ensure blog is valid
                    .map((blog, index) => (
                      <BlogListItem
                        key={blog._id?.toString() || index}
                        blog={blog}
                      />
                    ))
                ) : (
                  <View className="bg-white rounded-lg p-4 mx-2">
                    <AppText className="text-center text-gray-500">
                      No blogs in this section
                    </AppText>
                  </View>
                )}
              </View>
            )}
          </View>
        );
      })}
    </View>
  );
}
