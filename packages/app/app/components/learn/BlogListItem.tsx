import AppText from "@components/common/AppText";
import { LearnNavParams } from "@navigation/learn-navigator/LearnNavParams";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Image, StyleSheet, View } from "react-native";
import { Blog } from "../../../../shared/types/Blog";
// @ts-ignore
import Pressable from "@components/common/Pressable";
import { useSession } from "@hooks/persistUser";
import mixpanel from "@utils/mixpanel";

type Props = {
  blog: Blog;
};

export default function BlogListItem(props: Props) {
  const navigation = useNavigation<StackNavigationProp<LearnNavParams>>();
  const { user } = useSession();

  const onBlogItemPress = async () => {
    navigation.navigate("BlogDetailsScreen", { blog: String(props.blog._id) });
    mixpanel.trackEvent(
      "Learn post card clicked",
      {
        screen_name: "Learn post list",
        post_name: props?.blog?.title || "",
        post_id: props?.blog?._id?.toString() || "",
        email: user?.email || "",
        phone: user?.contact?.phone || "",
      },
      String(user?._id),
      "v2"
    );
  };

  return (
    <Pressable
      actionTag="service card item"
      className="bg-white rounded-lg pr-4 mt-2 min-h-[80px]"
      style={styles.container}
      onPress={onBlogItemPress}
    >
      <View className="flex flex-row min-h-[80px]">
        <Image
          className="w-[80px] h-full rounded-md mr-4"
          source={{ uri: props?.blog?.banner }}
          resizeMode="cover"
        />
        <View className="flex flex-col justify-center flex-1">
          <AppText
            numberOfLines={2}
            ellipsizeMode="tail"
            className="text-base text-[#336D9F] font-montserratBold overflow-hidden whitespace-nowrap mr-2 mb-2"
          >
            {props.blog.title}
          </AppText>

          <View className="flex flex-row items-center">
            <AppText className="text-sm text-[#336D9F]" numberOfLines={2}>
              {props.blog.description}
            </AppText>
          </View>
        </View>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    elevation: 4,
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  likeImageStyles: { borderWidth: 2, borderColor: "#fff" },
});
